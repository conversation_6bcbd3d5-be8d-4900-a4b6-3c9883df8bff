"use client"

import React from "react"
import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { AutoResizeTextarea } from "@/components/ui/auto-resize-textarea"
import { Card, CardContent } from "@/components/ui/card"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink, MessageCircle, AlertCircle, Paperclip, X, Loader2, Settings } from "lucide-react"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { PlanningTask, Question } from "@/types/planning"
import { ResultsView } from "@/components/results-view"
import Image from "next/image"
import Globe from "@/components/Globe"

const getBasePlanningTasks = (): PlanningTask[] => [
  { id: "analyze", title: "Analyze project requirements", completed: false },
  { id: "clarify", title: "Gather additional details", completed: false },
  { id: "summary", title: "Generate project summary", completed: false },
  { id: "techstack", title: "Select technology stack", completed: false },
  { id: "prd", title: "Create requirements document", completed: false },
]

const getOptionalTasks = (projectType: string): PlanningTask[] => {
  const tasks: PlanningTask[] = []

  // Add context profile for AI agents
  if (isAIAgentProject(projectType)) {
    tasks.push({ id: "context-profile", title: "Generate AI agent context profile", completed: false })
  }

  // Add database design for apps that need databases
  if (needsDatabaseStep(projectType)) {
    tasks.push({ id: "database", title: "Design database schema", completed: false })
  }

  return tasks
}

const getCommonTasks = (): PlanningTask[] => [
  { id: "wireframes", title: "Design UI wireframes", completed: false },
  { id: "design", title: "Create design guidelines", completed: false },
  { id: "filesystem", title: "Plan file structure", completed: false },
  { id: "workflow", title: "Define workflow logic", completed: false },
  { id: "tasks", title: "Break down implementation tasks", completed: false },
  { id: "scaffold", title: "Generate project scaffold", completed: false },
]

const isAIAgentProject = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("agent") ||
         lowerType.includes("bot") ||
         lowerType.includes("ai") ||
         lowerType === "ai agent"
}

const needsDatabaseStep = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("web") ||
         lowerType.includes("app") ||
         lowerType.includes("api") ||
         lowerType.includes("system")
}

const generateDynamicTasks = (projectType: string): PlanningTask[] => {
  return [
    ...getBasePlanningTasks(),
    ...getOptionalTasks(projectType),
    ...getCommonTasks()
  ]
}

export function PlanningAgent() {
  return <div>Test</div>
}

export function PlanningAgentOld() {
  const [userPrompt, setUserPrompt] = useState("")
  const [hasStarted, setHasStarted] = useState(false)
  const [isInteractive, setIsInteractive] = useState(false)
  const [tasks, setTasks] = useState<PlanningTask[]>(getBasePlanningTasks())
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<Record<string, any>>({})
  const [showResults, setShowResults] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [questionAnswer, setQuestionAnswer] = useState("")
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  const [planningContext, setPlanningContext] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [canRetry, setCanRetry] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [isProcessingImages, setIsProcessingImages] = useState(false)
  const [designStyleGuideState, setDesignStyleGuideState] = useState<string | null>(null)

  // Settings state
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [userApiKey, setUserApiKey] = useState("")
  const [preferredModel, setPreferredModel] = useState("anthropic/claude-sonnet-4")
  const [isAutonomousMode, setIsAutonomousMode] = useState(true)

  // UI state
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning">("preview")
  const [chatMessages, setChatMessages] = useState<Array<{id: string, type: 'user' | 'ai', content: string, timestamp: Date}>>([])
  const [chatInput, setChatInput] = useState("")
  const [sidebarWidth, setSidebarWidth] = useState(500) // Default width for SSR (1/3 page width)
  const [isResizing, setIsResizing] = useState(false)
  const [selectedPlanningSection, setSelectedPlanningSection] = useState<string | null>(null)

  const taskListRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const resizeRef = useRef<HTMLDivElement>(null)
  // Removed isUsingFallback - using AI service only

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])

  // Resize functionality
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return

      const newWidth = e.clientX - 16 // Account for padding
      const minWidth = 300
      const maxWidth = window.innerWidth * 0.6

      if (newWidth >= minWidth && newWidth <= maxWidth) {
        setSidebarWidth(newWidth)
      }
    }

    const handleMouseUp = () => {
      setIsResizing(false)
    }

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing])

  // Auto-scroll to current task
  useEffect(() => {
    if (taskListRef.current && currentTaskIndex >= 0) {
      const taskElements = taskListRef.current.querySelectorAll('[data-task-index]')
      const currentTaskElement = taskElements[currentTaskIndex] as HTMLElement

      if (currentTaskElement) {
        const container = taskListRef.current
        const containerHeight = container.clientHeight
        const taskTop = currentTaskElement.offsetTop
        const taskHeight = currentTaskElement.offsetHeight

        // Calculate scroll position to center the current task
        const scrollTop = taskTop - (containerHeight / 2) + (taskHeight / 2)

        container.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        })
      }
    }
  }, [currentTaskIndex, tasks.length])

  const processNextTask = async (context: any) => {
    console.log(`processNextTask called with currentTaskIndex: ${currentTaskIndex}`)

    if (currentTaskIndex < 0 || currentTaskIndex >= tasks.length) {
      console.log("Invalid task index, finishing processing")
      setIsProcessing(false)
      return
    }

    const currentTask = tasks[currentTaskIndex]
    if (!currentTask) {
      console.log("No current task found, finishing processing")
      setIsProcessing(false)
      return
    }

    console.log(`Processing task: ${currentTask.id} (index: ${currentTaskIndex})`)
    console.log(`Context keys:`, Object.keys(context || {}))
    console.log(`Has design style guide:`, !!context.designStyleGuide)
    if (context?.designStyleGuide) {
      console.log("Design style guide length:", context.designStyleGuide.length)
    }

    // Ensure design style guide is preserved from multiple sources
    const designStyleGuide = context.designStyleGuide || planningContext?.designStyleGuide || designStyleGuideState
    console.log("Design style guide sources:")
    console.log("- context.designStyleGuide:", !!context.designStyleGuide)
    console.log("- planningContext?.designStyleGuide:", !!planningContext?.designStyleGuide)
    console.log("- designStyleGuideState:", !!designStyleGuideState)
    console.log("- final designStyleGuide:", !!designStyleGuide)
    if (designStyleGuide && !context.designStyleGuide) {
      console.log("Restoring design style guide from state/planningContext")
    }

    try {
      const response = await fetch("/api/planning/step", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          step: currentTask.id,
          context: {
            ...context,
            prompt: userPrompt,
            isInteractive: isInteractive,
            userAnswers: userAnswers,
            designStyleGuide: designStyleGuide, // Always include design style guide if available
            hasImages: uploadedImages.length > 0,
            // Include user preferences for each step
            userPreferences: {
              model: preferredModel,
              apiKey: userApiKey || undefined,
              autonomousMode: isAutonomousMode
            }
          },
          answer: questionAnswer,
        }),
      })

      // Check if response is ok first
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`API Error ${response.status}:`, errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
      }

      // Check content type
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        const textResponse = await response.text()
        console.error("Non-JSON response received:", textResponse.substring(0, 200))
        throw new Error("Server returned non-JSON response")
      }

      const responseText = await response.text()
      console.log(`API response for ${currentTask.id}:`, responseText.substring(0, 200) + "...")

      let result
      try {
        result = JSON.parse(responseText)
        console.log(`Parsed result for ${currentTask.id}:`, result)
      } catch (parseError) {
        console.error("Failed to parse response as JSON:", parseError)
        console.error("Raw response:", responseText.substring(0, 500))
        throw new Error("Invalid JSON response from API")
      }

      // Validate result structure
      if (!result || typeof result !== "object") {
        throw new Error("Invalid result structure")
      }

      // Check if we need user input
      if (result.needsInput && result.question && isInteractive) {
        console.log("Need user input:", result.question.question)
        setCurrentQuestion(result.question)
        setPlanningContext({ ...context, ...result })
        return
      }

      // Check if there's an error but we have fallback results
      if (result.error && result.results) {
        console.log("Using fallback results due to error:", result.error)
        setError(`Step processing issue: ${result.error}`)
      }

      // Mark task as completed and store results
      setTasks((prev) => {
        const updated = prev.map((task, index) => (index === currentTaskIndex ? { ...task, completed: true } : task))
        console.log(`Marked task ${currentTaskIndex} (${currentTask.id}) as completed`)
        return updated
      })

      // If this is the analyze step, update the task list based on project type
      if (currentTask.id === "analyze" && result.results?.analyze?.projectType) {
        const projectType = result.results.analyze.projectType
        console.log(`Project type detected: ${projectType}, updating task list...`)
        const newTasks = generateDynamicTasks(projectType)

        // Preserve completion status for already completed tasks
        const updatedTasks = newTasks.map((newTask, index) => {
          if (index < currentTaskIndex) {
            // Tasks before current should be completed
            return { ...newTask, completed: true }
          } else if (index === currentTaskIndex) {
            // Current task should be marked as completed since we just finished it
            return { ...newTask, completed: true }
          }
          return newTask
        })

        setTasks(updatedTasks)
        console.log(`Updated task list with ${updatedTasks.length} tasks for project type: ${projectType}`)
      }

      // Store the result data
      if (result.results && result.results[currentTask.id]) {
        // For all tasks, store the result returned from the planning step API.
        // The API already handles design step correctly by returning Gemini design when available
        setResults((prev) => ({ ...prev, [currentTask.id]: result.results[currentTask.id] }));
        console.log(`Stored result for ${currentTask.id}:`, result.results[currentTask.id]);
      } else {
        // Warn if no result data is available for any step.
        console.warn(`No result data for ${currentTask.id}`);
      }

      // Update planning context while preserving design style guide
      const preservedDesignStyleGuide = designStyleGuide || context.designStyleGuide || planningContext?.designStyleGuide || designStyleGuideState
      setPlanningContext({
        ...context,
        ...result,
        designStyleGuide: preservedDesignStyleGuide
      })

      // Move to next task
      if (currentTaskIndex < tasks.length - 1) {
        console.log(`Moving to next task (${currentTaskIndex + 1})`)
        setTimeout(() => {
          setCurrentTaskIndex((prev) => prev + 1)
        }, 1000)
      } else {
        console.log("All tasks completed!")
        // Add delay to allow UI to update and show final task as completed
        setTimeout(() => {
          setIsProcessing(false)
        }, 1000)
      }
    } catch (error) {
      console.error("Failed to process step:", error)

      // Enhanced error handling for step processing
      let errorMessage = `AI step processing failed for ${currentTask.title}. `
      let shouldRetry = false

      if (error instanceof Error) {
        if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          shouldRetry = true
        } else if (error.message.includes("timeout")) {
          errorMessage += "Request timed out."
          shouldRetry = true
        } else if (error.message.includes("network")) {
          errorMessage += "Network error occurred."
          shouldRetry = true
        } else {
          errorMessage += error.message
        }
      } else {
        errorMessage += "Unknown error occurred."
      }

      setError(errorMessage + (shouldRetry ? " You can try again." : " Please check your AI service configuration."))
      setCanRetry(shouldRetry)

      // Stop processing on error - no fallback to mock data
      setIsProcessing(false)
    }
  }

  const handleStartPlanning = async () => {
    if (!userPrompt.trim()) return

    console.log("Starting planning process...")
    setHasStarted(true)
    setIsProcessing(true)
    setCurrentTaskIndex(0)
    setError(null)

    // Add user message to chat
    const userMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: userPrompt,
      timestamp: new Date()
    }
    setChatMessages(prev => [...prev, userMessage])

    // Add AI response
    setTimeout(() => {
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai' as const,
        content: "I'll help you build that! Let me start by analyzing your requirements and creating a development plan.",
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, aiMessage])
    }, 500)

    try {
      let designStyleGuide = null

      // If images are uploaded, use the design agent first
      if (uploadedImages.length > 0) {
        console.log("Processing uploaded images with design agent...")
        setIsProcessingImages(true)

        try {
          const formData = new FormData()
          uploadedImages.forEach((image, index) => {
            formData.append('images', image)
          })

          const designResponse = await fetch("/api/design-agent", {
            method: "POST",
            body: formData,
          })

          if (designResponse.ok) {
            const designResult = await designResponse.json()
            designStyleGuide = designResult.styleGuide
            setDesignStyleGuideState(designStyleGuide) // Store in state for persistence
            console.log("Design agent generated style guide - will be added when design step runs")
          } else {
            const errorText = await designResponse.text()
            console.warn("Design agent failed:", errorText)
            // Continue without style guide but show a warning
          }
        } catch (designError) {
          console.error("Design agent error:", designError)
          // Continue without style guide
        } finally {
          setIsProcessingImages(false)
        }
      }

      // Test the API connection first
      const response = await fetch("/api/planning", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: userPrompt,
          isInteractive,
          answers: userAnswers,
          designStyleGuide, // Include the generated style guide
          hasImages: uploadedImages.length > 0,
          // Include user preferences
          userPreferences: {
            model: preferredModel,
            apiKey: userApiKey || undefined,
            autonomousMode: isAutonomousMode
          }
        }),
      })

      const responseText = await response.text()
      console.log("Initial API response:", responseText)

      let result
      try {
        result = JSON.parse(responseText)
      } catch (parseError) {
        console.error("Failed to parse initial response:", parseError)
        throw new Error("Invalid JSON response from planning API")
      }

      if (!response.ok) {
        console.error("API returned error:", result)
        throw new Error(`API Error: ${response.status} - ${result.error || "Unknown error"}`)
      }

      console.log("Using AI-powered planning mode")
      console.log("Setting planning context with design style guide:", !!designStyleGuide)
      setPlanningContext({
        ...result,
        designStyleGuide: designStyleGuide, // Ensure the design style guide is included and takes precedence
      })
      // Start AI-based processing
      console.log("Setting currentTaskIndex to 0 and starting processing")
      setCurrentTaskIndex(0)
    } catch (error) {
      console.error("Failed to start AI planning:", error)

      // Enhanced error handling with specific error types
      let errorMessage = "AI planning failed. "
      let suggestion = ""

      if (error instanceof Error) {
        if (error.message.includes("401") || error.message.includes("403")) {
          errorMessage += "Authentication failed."
          suggestion = "Please check your OPENROUTER_API_KEY is valid and has sufficient credits."
        } else if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          suggestion = "Please wait a moment and try again."
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage += "Network connection failed."
          suggestion = "Please check your internet connection and try again."
        } else {
          errorMessage += error.message
          suggestion = "Please check your OPENROUTER_API_KEY and try again."
        }
      } else {
        errorMessage += "Unknown error occurred."
        suggestion = "Please refresh the page and try again."
      }

      setError(`${errorMessage} ${suggestion}`)
      // Do not fallback to mock data - show error instead
      setIsProcessing(false)
    }
  }

  // All mock data generation removed - using AI service only

  // All mock wireframe generation removed

  // All mock helper functions removed - using AI service only

  const retryCurrentStep = () => {
    setError(null)
    setCanRetry(false)
    setIsProcessing(true)

    // Retry the current step
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length) {
      setTimeout(() => processNextTask(planningContext || {}), 500)
    } else {
      // Retry from the beginning
      setTimeout(() => handleStartPlanning(), 500)
    }
  }

  const handleQuestionSubmit = async () => {
    if (currentQuestion) {
      setUserAnswers((prev) => ({ ...prev, [currentQuestion.id]: questionAnswer }))
      setQuestionAnswer("")
      setCurrentQuestion(null)

      // Continue processing with the answer using AI
      setTimeout(() => processNextTask(planningContext), 500)
    }
  }

  const handleQuestionKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleQuestionSubmit()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleStartPlanning()
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const imageFiles = Array.from(files).filter(file => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          return false
        }
        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`)
          return false
        }
        return true
      })

      // Limit total number of images to 5
      const currentCount = uploadedImages.length
      const newFiles = imageFiles.slice(0, Math.max(0, 5 - currentCount))

      if (newFiles.length < imageFiles.length) {
        alert(`Maximum 5 images allowed. Only the first ${newFiles.length} images were added.`)
      }

      setUploadedImages(prev => [...prev, ...newFiles])
    }
    // Reset the input value to allow uploading the same file again
    e.target.value = ''
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const triggerImageUpload = () => {
    fileInputRef.current?.click()
  }

  const clearAllImages = () => {
    setUploadedImages([])
  }

  useEffect(() => {
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length && isProcessing && !currentQuestion) {
      console.log(`useEffect triggered for task index: ${currentTaskIndex}, task: ${tasks[currentTaskIndex]?.id}`)
      processNextTask(planningContext || {})
    }
  }, [currentTaskIndex, isProcessing, currentQuestion])

  // Auto-select first section when planning completes
  useEffect(() => {
    if (!isProcessing && Object.keys(results).length > 0 && !selectedPlanningSection) {
      const firstSection = Object.keys(results)[0]
      if (firstSection) {
        setSelectedPlanningSection(firstSection)
      }
    }
  }, [isProcessing, results, selectedPlanningSection])

  const openResults = () => {
    setShowResults(true)
  }

  const backToPlanning = () => {
    setShowResults(false)
  }

  // Simple rendering functions for planning content
  const renderSectionContent = (sectionId: string, data: any) => {
    return (
      <div className="space-y-4">
        <div className="bg-red-900/20 border border-red-500/30 rounded p-3 text-xs">
          <div className="text-red-400 font-medium mb-2">Debug - Data Structure:</div>
          <pre className="text-gray-300 whitespace-pre-wrap">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
        <div className="text-gray-300">
          {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
        </div>
      </div>
    )
  }

  if (showResults) {
    return <ResultsView results={results} userPrompt={userPrompt} onBack={backToPlanning} />
  }

  // Question overlay (only shown in interactive mode)
  if (currentQuestion && isInteractive) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <MessageCircle className="w-5 h-5 text-red-400" />
              <span className="text-sm text-gray-400">AG3NT is asking</span>
            </div>

            <h2 className="text-2xl font-light text-white">{currentQuestion.question}</h2>

            <div className="space-y-4">
              {currentQuestion.type === "text" ? (
                <Input
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full h-12 text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400"
                  autoFocus
                />
              ) : (
                <Textarea
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full min-h-[100px] text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400 resize-none"
                  autoFocus
                />
              )}

              <div className="flex gap-3">
                <Button
                  onClick={handleQuestionSubmit}
                  disabled={!questionAnswer.trim() && !currentQuestion.optional}
                  className="flex-1 h-12 bg-red-600 hover:bg-red-700 text-white rounded-none font-medium"
                >
                  Continue
                </Button>
                {currentQuestion.optional && (
                  <Button
                    onClick={() => {
                      setCurrentQuestion(null)
                      setTimeout(() => processNextTask(planningContext), 500)
                    }}
                    variant="outline"
                    className="h-12 px-6 border-gray-600 text-gray-300 hover:bg-gray-800 rounded-none"
                  >
                    Skip
                  </Button>
                )}
              </div>
            </div>

            {currentQuestion.optional && <p className="text-xs text-gray-500">This question is optional</p>}
          </motion.div>
        </div>
      </div>
    )
  }

  if (!hasStarted) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4 relative">
        {/* Settings Button - Bottom Left */}
        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed bottom-6 left-6 h-8 w-8 text-gray-400 hover:text-white transition-colors duration-200 z-50 bg-transparent border-0 p-0"
            >
              <Settings size={20} />
              <span className="sr-only">Settings</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md border-gray-700 text-white" style={{ backgroundColor: '#181818' }}>
            <DialogHeader>
              <DialogTitle className="text-white">Settings</DialogTitle>
            </DialogHeader>
            <div className="space-y-6 py-4">
              {/* API Key Input */}
              <div className="space-y-2">
                <Label htmlFor="api-key" className="text-sm font-medium text-gray-300">
                  OpenRouter API Key
                </Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="sk-or-v1-..."
                  value={userApiKey}
                  onChange={(e) => setUserApiKey(e.target.value)}
                  className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
                <p className="text-xs text-gray-400">
                  Optional: Use your own API key for unlimited usage
                </p>
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <Label htmlFor="model" className="text-sm font-medium text-gray-300">
                  Preferred Model
                </Label>
                <Select value={preferredModel} onValueChange={setPreferredModel}>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="border-gray-600" style={{ backgroundColor: '#000000' }}>
                    <SelectItem value="x-ai/grok-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Grok 4 (X.AI)</SelectItem>
                    <SelectItem value="moonshotai/kimi-k2" className="text-white hover:bg-gray-800 focus:bg-gray-800">Kimi K2 (Moonshot)</SelectItem>
                    <SelectItem value="anthropic/claude-sonnet-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude Sonnet 4 (Default)</SelectItem>
                    <SelectItem value="anthropic/claude-3.7-sonnet:thinking" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet (Thinking)</SelectItem>
                    <SelectItem value="anthropic/claude-3.7-sonnet" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet</SelectItem>
                    <SelectItem value="openai/gpt-4.1" className="text-white hover:bg-gray-800 focus:bg-gray-800">GPT-4.1 (OpenAI)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="autonomous-mode" className="text-sm font-medium text-gray-300">
                    Autonomous Mode
                  </Label>
                  <p className="text-xs text-gray-400">
                    {isAutonomousMode ? "AI runs all steps automatically" : "Guided step-by-step planning"}
                  </p>
                </div>
                <Switch
                  id="autonomous-mode"
                  checked={isAutonomousMode}
                  onCheckedChange={setIsAutonomousMode}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Powered by AP3X - Bottom Right */}
        <div className="fixed bottom-6 right-6 text-xs text-gray-500 font-medium z-50">
          Powered by{' '}
          <span className="text-white font-semibold">AP3</span>
          <span className="text-[#ff2d55] font-semibold" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
        </div>

        <div className="w-full max-w-2xl text-center space-y-8">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <div className="flex items-center justify-center gap-3 mb-6">
              <Image
                src="/AG3NT.png"
                alt="AG3NT"
                width={120}
                height={40}
                className="object-contain"
              />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Image Upload Preview */}
              {uploadedImages.length > 0 && (
                <div className="p-3 rounded-lg" style={{ backgroundColor: '#181818' }}>
                  {isProcessingImages && (
                    <div className="flex items-center gap-2 mb-3 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Analyzing images with AI...</span>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {uploadedImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Upload ${index + 1}`}
                          className={`w-16 h-16 object-cover rounded border transition-opacity ${
                            isProcessingImages ? 'opacity-50' : 'opacity-100'
                          }`}
                          title={image.name}
                        />
                        {!isProcessingImages && (
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X size={12} />
                          </button>
                        )}
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity truncate">
                          {image.name}
                        </div>
                      </div>
                    ))}
                  </div>
                  {uploadedImages.length > 0 && !isProcessingImages && (
                    <div className="mt-2 flex items-center justify-between">
                      <div className="text-xs text-gray-600">
                        {uploadedImages.length} image{uploadedImages.length > 1 ? 's' : ''} ready for AI analysis
                      </div>
                      <button
                        onClick={clearAllImages}
                        className="text-xs text-red-500 hover:text-red-700 underline"
                      >
                        Clear all
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Input with Paperclip and Send Button */}
              <div className="flex w-full items-end gap-0 rounded-3xl overflow-hidden bg-white shadow-lg border border-gray-100">
                <Button
                  onClick={triggerImageUpload}
                  className="flex-shrink-0 h-12 w-12 text-gray-600 bg-transparent border-0 hover:bg-gray-50 transition-colors duration-200 rounded-l-3xl"
                  title="Upload design reference images for AI analysis"
                  disabled={isProcessing || isProcessingImages}
                >
                  <Paperclip size={18} />
                  <span className="sr-only">Upload Images</span>
                </Button>

                <div className="flex-1 relative flex items-center">
                  <textarea
                    value={userPrompt}
                    onChange={(e) => {
                      setUserPrompt(e.target.value)
                      // Auto-resize logic
                      const textarea = e.target as HTMLTextAreaElement
                      textarea.style.height = 'auto'
                      const newHeight = Math.min(Math.max(textarea.scrollHeight, 48), 240) // 48px min, 240px max (10 lines)
                      textarea.style.height = `${newHeight}px`
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        if (userPrompt.trim()) {
                          handleStartPlanning()
                        }
                      }
                    }}
                    placeholder="What would you like to build?"
                    className="w-full h-12 max-h-[240px] py-3 px-4 text-base text-black placeholder-gray-500 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 scrollbar-hide"
                    style={{
                      height: '48px'
                    }}
                    autoFocus
                  />
                </div>

                <Button
                  onClick={handleStartPlanning}
                  disabled={!userPrompt.trim()}
                  className="flex-shrink-0 h-12 w-12 text-white bg-[#ff2d55] border-0 hover:bg-[#e6254d] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderTopRightRadius: '1.5rem',
                    borderBottomRightRadius: '1.5rem',
                    borderTopLeftRadius: '0',
                    borderBottomLeftRadius: '0'
                  }}
                >
                  <span className="sr-only">Start</span>
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                    <path d="M4 3v14l12-7L4 3z" fill="white"/>
                  </svg>
                </Button>
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>



          </motion.div>
        </div>
      </div>
    )
  }


}
